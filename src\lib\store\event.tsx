import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

export interface CreatedEventData {
  id: string;
  title: string;
  category_id: string;
  date_from: string;
  date_to: string;
  location_address: string;
  banner_image_id: string | null;
  created_at: string;
  updated_at: string;
}

type EventState = {
  createdEventData: CreatedEventData | null;
  setCreatedEventData: (eventData: CreatedEventData) => void;
  clearCreatedEventData: () => void;
};

export const useEventStore = create<EventState>()(
  persist(
    (set) => ({
      createdEventData: null,
      setCreatedEventData: (eventData) => set({ createdEventData: eventData }),
      clearCreatedEventData: () => set({ createdEventData: null }),
    }),
    {
      name: 'event-store',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
